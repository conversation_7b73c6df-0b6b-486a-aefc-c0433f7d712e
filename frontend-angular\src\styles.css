/* 🌍 Глобални стилове - важат за ЦЯЛОТО приложение */
/* Това са правила, които се прилагат навсякъде, не само в един компонент */

/* 📄 Стилове за цялата страница */
body { /* Правила за body (тялото на страницата) */
  font-family: Arial, sans-serif; /* ✏️ Използваме шрифт Arial (ако го няма - друг подобен) */
  background-color: lightblue; /* 🌊 Боядисваме фона в светлосиньо - като небето! */
  margin: 0; /* ⭕ Махаме празното място около страницата */
  padding: 20px; /* 🛏️ Слагаме 20 пиксела "възглавничка" отвътре */
} /* Край на стиловете за body */

/* 📦 Reset на някои стилове - правим ги еднакви навсякъде */
* { /* Звездичката означава "за ВСИЧКИ елементи" */
  box-sizing: border-box; /* 📏 Padding и border се броят в размера на кутията */
} /* Това прави изчисленията по-лесни! */

/* 📝 Нулираме margin на заглавия и параграфи */
h1, h2, h3, p { /* За всички заглавия (h1, h2, h3) и параграфи (p) */
  margin: 0; /* ⭕ Махаме автоматичното място около тях */
} /* Така контролираме точно къде искаме място */
