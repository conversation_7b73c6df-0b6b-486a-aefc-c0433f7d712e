# 🚀 WEATHER STATION - БЪРЗ СТАРТ

## 🎯 САМО ЕДНА КОМАНДА:

```cmd
smart-start.bat
```

Това е всичко! Скриптът автоматично ще:
- ✅ Провери версията на Node.js
- ✅ Избере правилния начин за стартиране
- ✅ Стартира backend и frontend
- ✅ Отвори браузъра

---

## 📊 ВАШАТА СИТУАЦИЯ:

- **Node.js версия**: v14.17.3 (стара)
- **Необходима за Angular**: v18.13+
- **Статус**: ⚠️ Ще използваме алтернативна версия

---

## 🔧 ВСИЧКИ ОПЦИИ:

### За интерактивно меню:
```cmd
all-options.bat
```

### За проверка на версии:
```cmd
check-versions.bat
```

### За директно стартиране (Node 14):
```cmd
cd frontend-angular
run-simple.bat
```

---

## 📁 КАКВО ИМАТЕ:

```
weather/
├── smart-start.bat      ⭐ ИЗПОЛЗВАЙТЕ ТОВА!
├── all-options.bat      📋 Меню с всички опции
├── check-versions.bat   📊 Проверка на версии
├── backend-node/        🟢 Node.js backend
└── frontend-angular/    🅰️ Angular frontend
    └── simple-server.js 🎯 Работи с Node 14
```

---

## ❓ ВЪПРОСИ И ОТГОВОРИ:

**В: Трябва ли да актуализирам Node.js?**
О: Не е задължително! Използвайте `smart-start.bat`

**В: Кой файл да стартирам?**
О: `smart-start.bat` - той ще избере сам

**В: Нищо не работи!**
О: Изпълнете `check-versions.bat` и изпратете резултата

---

🎉 **Готово! Просто стартирайте `smart-start.bat`!**
