# 🌦️ Weather Station
<!-- Главният README - като корицата на книга! -->

Просто приложение за времето с .NET backend и два frontend варианта (Vanilla JS и Angular).

## 📁 Структура на проекта

```
weather/                    # 🏠 Главната папка
├── backend/               # 🖥️ .NET 8.0 Web API (сървърът)
├── frontend/              # 🌐 Оригинален vanilla HTML/JS/CSS frontend
└── frontend-angular/      # 🅰️ Angular 17 frontend (новият)
```

## 🖥️ Backend

- **Технология**: ASP.NET Core 8.0 Minimal API <!-- Модерен C# сървър -->
- **Endpoint**: `GET /weather/{country}` <!-- Път за получаване на данни -->
- **Порт**: 5240 <!-- Номерът на "вратата" -->
- **CORS**: Конфигуриран за localhost:3000 <!-- Позволява на frontend-а да говори със сървъра -->

### ▶️ Стартиране на Backend

```bash
cd backend/WeatherApi  # 📁 Влизаме в папката
dotnet run  # 🚀 Стартираме сървъра
```

## 🎨 Frontend Опции

### 1. Vanilla JavaScript (Оригинален) <!-- Простият вариант -->

Просто HTML/CSS/JavaScript имплементация.

```bash
cd frontend  # 📁 Влизаме в папката
# Сервирай index.html на порт 3000  # 🌐 Отвори файла в браузър
```

### 2. Angular (Нов) <!-- Модерният вариант -->

Модерна Angular 17 имплементация с TypeScript.

```bash
cd frontend-angular  # 📁 Влизаме в папката
npm install  # 📦 Инсталираме библиотеките (само първия път)
npm start  # 🚀 Стартираме приложението
```

## ✨ Функционалности

- 🌍 Избор на държава (България & UK) <!-- Можеш да избираш -->
- 🌡️ Показване на текущото време <!-- Виждаш температурата -->
- 💨 Скорост на вятъра и влажност <!-- Допълнителна информация -->
- 📊 24-часова прогноза <!-- Планираш деня си -->
- 📱 Responsive дизайн <!-- Работи навсякъде -->

## 🎓 Образователна цел

Този проект включва подробни коментари на български език, обясняващи концепциите на програмирането, което го прави идеален за изучаване на full-stack разработка.

<!-- 📚 Всеки ред код е обяснен като за деца! -->
<!-- 🎯 Перфектен за начинаещи програмисти -->
<!-- 💡 Научаваш и frontend, и backend -->

## 🚀 Бърз старт

1. **Инсталирай изискванията**:
   - .NET 8.0 SDK за backend <!-- За C# сървъра -->
   - Node.js 18+ за Angular frontend <!-- За модерния frontend -->

2. **Стартирай backend**:
   ```bash
   cd backend/WeatherApi && dotnet run
   ```

3. **Стартирай frontend** (избери един):
   - Vanilla: Отвори `frontend/index.html` в браузър
   - Angular: `cd frontend-angular && npm install && npm start`

4. **Отвори браузъра**: http://localhost:3000

<!-- 🎉 Готово! Вече имаш работещо Weather Station приложение! -->
