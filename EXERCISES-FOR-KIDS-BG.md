# 🎮 Упражнения за Weather Station

## 🌟 Ниво 1: Начинаещи (Лесни промени)

### Упражнение 1: Промени цветовете 🎨
**Файл:** `src/app/app.component.css`

**Задача:** Направи температурата червена вместо синя.

**Търси:**
```css
.temperature {
  color: blue;
}
```

**Промени на:**
```css
.temperature {
  color: red;
}
```

**Обяснение:** Цветът `red` означава червено на английски.

---

### Упражнение 2: Промени приветствието 👋
**Файл:** `src/app/app.component.html`

**Задача:** Промени "Select Country:" на "Избери държава:"

**Търси:**
```html
<label for="countrySelect">Select Country:</label>
```

**Промени на:**
```html
<label for="countrySelect">Избери държава:</label>
```

**Обяснение:** Сега текстът е на български език.

---

### Упражнение 3: Добави емотикони 😊
**Файл:** `backend-node/server.js`

**Задача:** Добави емотикони към описанието на времето.

**Търси:**
```javascript
description: "Sunny",
```

**Промени на:**
```javascript
description: "Sunny ☀️",
```

**И също:**
```javascript
description: "Rainy",
```

**Промени на:**
```javascript
description: "Rainy 🌧️",
```

**Обяснение:** Емотиконите правят приложението по-весело!

---

## 🌟 Ниво 2: Напреднали (Добавяне на нови неща)

### Упражнение 4: Добави нова държава 🌍
**Файл:** `backend-node/server.js`

**Задача:** Добави Германия в списъка.

**След данните за UK добави:**
```javascript
germany: {
  location: "Berlin, Germany",
  temperature: "15°",
  description: "Cloudy ☁️",
  windSpeed: "18 km/h",
  humidity: "65%",
  forecast: [
    { time: "00:00", temp: "12°" },
    { time: "03:00", temp: "10°" },
    { time: "06:00", temp: "11°" },
    { time: "09:00", temp: "14°" },
    { time: "12:00", temp: "17°" },
    { time: "15:00", temp: "18°" },
    { time: "18:00", temp: "15°" },
    { time: "21:00", temp: "13°" }
  ]
}
```

**И в файл:** `src/app/app.component.ts`

**Добави в списъка countries:**
```typescript
countries: Country[] = [
  { value: 'bulgaria', name: 'Bulgaria' },
  { value: 'uk', name: 'United Kingdom' },
  { value: 'germany', name: 'Germany' }  // Нова държава!
];
```

**Обяснение:** Сега имаш три държави в менюто!

---

### Упражнение 5: Промени размера на текста 📏
**Файл:** `src/app/app.component.css`

**Задача:** Направи температурата още по-голяма.

**Търси:**
```css
.temperature {
  font-size: 48px;
}
```

**Промени на:**
```css
.temperature {
  font-size: 72px;
}
```

**Обяснение:** `font-size` определя размера на буквите. По-голямо число = по-големи букви.

---

### Упражнение 6: Добави нова кутийка с информация 📦
**Файл:** `src/app/app.component.html`

**Задача:** Добави кутийка за "Feels Like" (Усеща се като).

**След кутийката за Humidity добави:**
```html
<div class="detail-box">
  <div class="detail-title">Feels Like</div>
  <div class="detail-value">20°</div>
</div>
```

**Обяснение:** Сега имаш три кутийки с информация!

---

## 🌟 Ниво 3: Експерти (Създаване на нови функции)

### Упражнение 7: Добави бутон за обновяване 🔄
**Файл:** `src/app/app.component.html`

**Задача:** Добави бутон, който обновява данните.

**След country-selector div добави:**
```html
<div style="text-align: center; margin: 10px;">
  <button (click)="loadWeather()" class="retry-button">
    🔄 Обнови
  </button>
</div>
```

**Обяснение:** `(click)="loadWeather()"` казва какво да се случи при натискане.

---

### Упражнение 8: Добави анимация 🎬
**Файл:** `src/app/app.component.css`

**Задача:** Направи температурата да пулсира.

**Добави в края на файла:**
```css
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.temperature {
  animation: pulse 2s infinite;
}
```

**Обяснение:** 
- `@keyframes` създава анимация с име "pulse"
- `transform: scale()` уголемява и смалява
- `animation: pulse 2s infinite` пуска анимацията за 2 секунди, безкрайно

---

### Упражнение 9: Промени фона според времето 🌈
**Файл:** `src/app/app.component.ts`

**Задача:** Промени фона когато е слънчево или дъждовно.

**В метода loadWeather(), след `this.weatherData = data;` добави:**
```typescript
// Промени фона според времето
if (data && data.description) {
  const body = document.body;
  if (data.description.includes('Sunny')) {
    body.style.backgroundColor = '#87CEEB'; // Небесносиньо
  } else if (data.description.includes('Rainy')) {
    body.style.backgroundColor = '#708090'; // Сиво
  } else {
    body.style.backgroundColor = 'lightblue'; // По подразбиране
  }
}
```

**Обяснение:** 
- `document.body` е цялата страница
- `includes()` проверява дали текстът съдържа дума
- `style.backgroundColor` променя цвета на фона

---

## 🎯 Предизвикателства

### Предизвикателство 1: Направи тъмна тема 🌙
- Промени белия фон на черен
- Промени черния текст на бял
- Добави бутон за превключване

### Предизвикателство 2: Добави звукови ефекти 🔊
- Звук при смяна на държава
- Различни звуци за слънчево/дъждовно

### Предизвикателство 3: Запази любима държава 💾
- Използвай localStorage
- При отваряне покажи последно избраната държава

### Предизвикателство 4: Добави график 📊
- Покажи температурата като график
- Използвай Canvas или SVG

### Предизвикателство 5: Направи игра 🎮
- Покажи температура и накарай потребителя да познае държавата
- Добави точки за правилни отговори

---

## 💡 Съвети за упражненията:

1. **Запазвай често** 💾
   - Ctrl+S след всяка промяна
   - Браузърът ще се обнови автоматично

2. **Ако нещо се счупи** 🔧
   - Ctrl+Z връща назад
   - Провери за липсващи скоби или кавички

3. **Експериментирай** 🧪
   - Пробвай различни цветове
   - Пробвай различни размери
   - Не се страхувай да грешиш!

4. **Използвай конзолата** 🖥️
   - F12 отваря Developer Tools
   - Там се показват грешките

5. **Питай за помощ** 🤝
   - Няма срамни въпроси
   - Всеки програмист е бил начинаещ

---

## 🏆 Система за точки:

- Ниво 1 упражнения: 10 точки всяко
- Ниво 2 упражнения: 20 точки всяко  
- Ниво 3 упражнения: 30 точки всяко
- Предизвикателства: 50 точки всяко

**Цели:**
- 50 точки: Начинаещ програмист 🌱
- 100 точки: Напреднал програмист 🌿
- 200 точки: Експерт програмист 🌳
- 300+ точки: Супер програмист! 🚀

---

Успех с упражненията! 🎉
