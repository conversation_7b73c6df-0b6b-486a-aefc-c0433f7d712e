// 🌟 Това е файлът с интерфейси - като шаблони за нашите данни
// Интерфейсите са като формички за сладки - казват каква форма трябва да имат данните

// 🌡️ Интерфейс за един елемент от прогнозата (един час от деня)
export interface ForecastItem { // Правим "формичка" за един час от прогнозата
  time: string;   // ⏰ Часът като текст (например "12:00")
  temp: string;   // 🌡️ Температурата като текст (например "25°")
} // Край на формичката за прогноза

// 🌤️ Интерфейс за цялата информация за времето
export interface WeatherInfo { // Правим голяма "формичка" за всичко за времето
  location: string;      // 📍 Мястото (например "София, България")
  temperature: string;   // 🌡️ Сегашната температура (например "22°")
  description: string;   // 📝 Описание (например "Слънчево")
  windSpeed: string;     // 💨 Скорост на вятъра (например "15 km/h")
  humidity: string;      // 💧 Влажност (например "5%")
  forecast: ForecastItem[]; // 📊 Списък с прогнозите за деня (масив от ForecastItem)
} // Край на голямата формичка

// 🌍 Интерфейс за държава в падащото меню
export interface Country { // Формичка за една държава в менюто
  value: string;  // 🔑 Кодът на държавата (например "bulgaria")
  name: string;   // 🏷️ Името на държавата (например "България")
} // Край на формичката за държава
