// 🌟 Това е файлът с интерфейси - като шаблони за нашите данни
// Интерфейсите са като формички за сладки - казват каква форма трябва да имат данните

// ========== LEGACY INTERFACES (за съвместимост с текущия UI) ==========

// 🌡️ Интерфейс за един елемент от прогнозата (един час от деня)
export interface ForecastItem { // Правим "формичка" за един час от прогнозата
  time: string;   // ⏰ Часът като текст (например "12:00")
  temp: string;   // 🌡️ Температурата като текст (например "25°")
} // Край на формичката за прогноза

// 🌤️ Интерфейс за цялата информация за времето (legacy format)
export interface WeatherInfo { // Правим голяма "формичка" за всичко за времето
  location: string;      // 📍 Мястото (например "София, България")
  temperature: string;   // 🌡️ Сегашната температура (например "22°")
  description: string;   // 📝 Описание (например "Слънчево")
  windSpeed: string;     // 💨 Скорост на вятъра (например "15 km/h")
  humidity: string;      // 💧 Влажност (например "5%")
  forecast: ForecastItem[]; // 📊 Списък с прогнозите за деня (масив от ForecastItem)
} // Край на голямата формичка

// 🌍 Интерфейс за държава в падащото меню
export interface Country { // Формичка за една държава в менюто
  value: string;  // 🔑 Кодът на държавата (например "bulgaria")
  name: string;   // 🏷️ Името на държавата (например "България")
} // Край на формичката за държава

// ========== OPENWEATHERMAP API INTERFACES ==========

// 🌐 Координати от OpenWeatherMap API
export interface OpenWeatherCoord {
  lon: number;  // 🌍 Географска дължина
  lat: number;  // 🌍 Географска ширина
}

// 🌦️ Информация за времето от OpenWeatherMap API
export interface OpenWeatherCondition {
  id: number;          // 🆔 ID на времето (например 800 за ясно небе)
  main: string;        // 🏷️ Основна група (например "Clear", "Rain", "Clouds")
  description: string; // 📝 Подробно описание (например "clear sky", "light rain")
  icon: string;        // 🖼️ Код на иконата (например "01d", "10n")
}

// 🌡️ Основни метеорологични данни от OpenWeatherMap API
export interface OpenWeatherMain {
  temp: number;       // 🌡️ Текуща температура
  feels_like: number; // 🤔 Усещана температура
  temp_min: number;   // 🔽 Минимална температура
  temp_max: number;   // 🔼 Максимална температура
  pressure: number;   // 📊 Атмосферно налягане (hPa)
  humidity: number;   // 💧 Влажност (%)
  sea_level?: number; // 🌊 Налягане на морското равнище (опционално)
  grnd_level?: number; // 🏔️ Налягане на земното равнище (опционално)
}

// 💨 Информация за вятъра от OpenWeatherMap API
export interface OpenWeatherWind {
  speed: number; // 💨 Скорост на вятъра (m/s)
  deg: number;   // 🧭 Посока на вятъра (градуси)
  gust?: number; // 💨 Порив на вятъра (опционално)
}

// ☁️ Информация за облачността от OpenWeatherMap API
export interface OpenWeatherClouds {
  all: number; // ☁️ Облачност (%)
}

// 🌧️ Информация за дъжда от OpenWeatherMap API (опционално)
export interface OpenWeatherRain {
  '1h'?: number; // 🌧️ Дъжд за последния час (mm)
  '3h'?: number; // 🌧️ Дъжд за последните 3 часа (mm)
}

// ❄️ Информация за снега от OpenWeatherMap API (опционално)
export interface OpenWeatherSnow {
  '1h'?: number; // ❄️ Сняг за последния час (mm)
  '3h'?: number; // ❄️ Сняг за последните 3 часа (mm)
}

// 🏛️ Системна информация от OpenWeatherMap API
export interface OpenWeatherSys {
  type?: number;   // 🔧 Вътрешен параметър
  id?: number;     // 🆔 Вътрешен ID
  country: string; // 🏳️ Код на държавата (например "BG", "GB")
  sunrise: number; // 🌅 Време на изгрев (Unix timestamp)
  sunset: number;  // 🌇 Време на залез (Unix timestamp)
}

// 🌍 Пълен отговор от OpenWeatherMap Current Weather API
export interface OpenWeatherResponse {
  coord: OpenWeatherCoord;        // 🌐 Координати
  weather: OpenWeatherCondition[]; // 🌦️ Масив с условия за времето
  base: string;                   // 🔧 Вътрешен параметър
  main: OpenWeatherMain;          // 🌡️ Основни данни
  visibility: number;             // 👁️ Видимост (метри)
  wind: OpenWeatherWind;          // 💨 Вятър
  clouds: OpenWeatherClouds;      // ☁️ Облаци
  rain?: OpenWeatherRain;         // 🌧️ Дъжд (опционално)
  snow?: OpenWeatherSnow;         // ❄️ Сняг (опционално)
  dt: number;                     // ⏰ Време на данните (Unix timestamp)
  sys: OpenWeatherSys;            // 🏛️ Системна информация
  timezone: number;               // 🕐 Часова зона (секунди от UTC)
  id: number;                     // 🆔 ID на града
  name: string;                   // 🏷️ Име на града
  cod: number;                    // 📊 Код на отговора (200 = успех)
}
