<!DOCTYPE html> <!-- 1: Казваме на браузъра "Хей, това е модерен HTML5 файл!" -->
<html lang="en"> <!-- 2: Започваме HTML документа и казваме, че е на английски език -->
<head> <!-- 3: Тук започва "главата" - място за важна информация, която не се вижда на екрана -->
    <meta charset="UTF-8"> <!-- 4: Казваме на браузъра да използва UTF-8 - така могат да се показват всички букви и емотикони! -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0"> <!-- 5: Правим страницата да изглежда добре на телефони и таблети -->
    <title>Simple Weather App</title> <!-- 6: Това е името, което се вижда в таба на браузъра - като име на книга! -->
    <!-- 7: Тук започват стиловете - те правят страницата красива! -->
    <style>
        /* ═══════ СТИЛОВЕ ЗА ЦЯЛАТА СТРАНИЦА ═══════ */
        body { /* 8: Това са правила за цялата страница (body = тяло) */
            font-family: Arial, sans-serif; /* 9: Избираме шрифт Arial - ако го няма, браузърът избира друг подобен */
            background-color: lightblue; /* 10: Боядисваме фона в светлосиньо - като небето! */
            margin: 0; /* 11: Махаме празното място около страницата */
            padding: 20px; /* 12: Слагаме 20 пиксела "възглавничка" отвътре */
        } /* 13: Край на правилата за body */

        /* ═══════ ГЛАВНАТА БЯЛА КУТИЯ ═══════ */
        .weather-app { /* 14: Правила за главната кутия на приложението */
            background-color: white; /* 15: Правим я бяла - като облаче! */
            max-width: 500px; /* 16: Не може да стане по-широка от 500 пиксела */
            margin: 0 auto; /* 17: Магия! Това я слага точно в средата */
            padding: 20px; /* 18: Слагаме "възглавничка" отвътре */
            border-radius: 10px; /* 19: Правим ъглите заоблени - като бонбон! */
            box-shadow: 0 4px 8px gray; /* 20: Добавяме сянка - сякаш кутията лети! */
        } /* 21: Край на правилата за weather-app */

        /* ═══════ МЯСТОТО ЗА ИЗБОР НА ДЪРЖАВА ═══════ */
        .country-selector { /* 22: Правила за частта с избора на държава */
            text-align: center; /* 23: Слагаме всичко в центъра */
            margin-bottom: 20px; /* 24: Оставяме място под него */
        } /* 25: Край на country-selector */

        .country-dropdown { /* 26: Правила за падащото меню */
            background-color: lightgray; /* 27: Светлосив фон - като мъгла */
            border: 2px solid gray; /* 28: Рамка от 2 пиксела в сиво */
            padding: 10px 20px; /* 29: "Възглавничка" - 10 горе/долу, 20 ляво/дясно */
            border-radius: 5px; /* 30: Леко заоблени ъгли */
            font-size: 16px; /* 31: Правим буквите с размер 16 */
            cursor: pointer; /* 32: Когато минем с мишката - става ръчичка! */
        } /* 33: Край на country-dropdown */

        /* ═══════ ГОРНАТА ЧАСТ С ТЕМПЕРАТУРАТА ═══════ */
        .header { /* 34: Правила за горната част */
            text-align: center; /* 35: Всичко в средата */
            margin-bottom: 20px; /* 36: Място под него */
        } /* 37: Край на header */

        .location { /* 38: Правила за текста с града */
            font-size: 18px; /* 39: Буквите са с размер 18 */
            margin: 0 0 10px 0; /* 40: 0 горе, 0 дясно, 10 долу, 0 ляво */
        } /* 41: Край на location */

        .temperature { /* 42: Правила за големите числа с температурата */
            font-size: 48px; /* 43: МНОГО големи числа - 48 пиксела! */
            font-weight: bold; /* 44: Дебели букви - като с маркер! */
            color: blue; /* 45: Сини числа */
            margin-bottom: 10px; /* 46: Малко място под тях */
        } /* 47: Край на temperature */

        .description { /* 48: Правила за описанието (слънчево, облачно...) */
            font-size: 16px; /* 49: Нормален размер на буквите */
            color: gray; /* 50: Сив цвят - не много тъмен */
            margin: 0; /* 51: Без допълнително място */
        } /* 52: Край на description */

        /* ═══════ КУТИЙКИТЕ С ВЯТЪР И ВЛАЖНОСТ ═══════ */
        .details { /* 53: Контейнер за двете кутийки */
            display: flex; /* 54: Магия! Подрежда кутийките една до друга */
            justify-content: space-around; /* 55: Разпределя ги равномерно */
            margin: 20px 0; /* 56: 20 пиксела място горе и долу */
        } /* 57: Край на details */

        .detail-box { /* 58: Правила за всяка малка кутийка */
            text-align: center; /* 59: Текстът в средата */
            background-color: lightgray; /* 60: Светлосив фон */
            padding: 15px; /* 61: "Възглавничка" от всички страни */
            border-radius: 5px; /* 62: Леко заоблени ъгли */
            width: 45%; /* 63: Всяка кутийка заема 45% от мястото */
        } /* 64: Край на detail-box */

        .detail-title { /* 65: Правила за заглавията (Wind Speed, Humidity) */
            font-size: 14px; /* 66: По-малки букви - 14 пиксела */
            color: gray; /* 67: Сив цвят */
            margin-bottom: 5px; /* 68: Малко място под тях */
        } /* 69: Край на detail-title */

        .detail-value { /* 70: Правила за стойностите (15 km/h, 5%) */
            font-size: 20px; /* 71: По-големи числа - 20 пиксела */
            font-weight: bold; /* 72: Дебели числа */
            color: black; /* 73: Черен цвят - да се виждат ясно */
        } /* 74: Край на detail-value */

        /* ═══════ ПРОГНОЗАТА ЗА ДНЕС ═══════ */
        .forecast { /* 75: Контейнер за цялата прогноза */
            margin-top: 20px; /* 76: Място отгоре */
        } /* 77: Край на forecast */

        .forecast-title { /* 78: Правила за заглавието на прогнозата */
            font-size: 18px; /* 79: Размер 18 пиксела */
            font-weight: bold; /* 80: Дебели букви */
            margin-bottom: 15px; /* 81: Място под заглавието */
            text-align: center; /* 82: В средата */
        } /* 83: Край на forecast-title */

        .forecast-items { /* 84: Контейнер за малките кутийки с часове */
            display: grid; /* 85: Използваме мрежа - като шахматна дъска! */
            grid-template-columns: repeat(4, 1fr); /* 86: Правим 4 еднакви колони */
            gap: 10px; /* 87: 10 пиксела между кутийките */
        } /* 88: Край на forecast-items */

        .forecast-item { /* 89: Правила за всяка малка кутийка с час */
            text-align: center; /* 90: Текстът в средата */
            background-color: lightgray; /* 91: Светлосив фон */
            padding: 8px; /* 92: Малка "възглавничка" */
            border-radius: 5px; /* 93: Заоблени ъгли */
        } /* 94: Край на forecast-item */

        .forecast-time { /* 95: Правила за часа (12:00, 15:00...) */
            font-size: 11px; /* 96: Много малки букви - 11 пиксела */
            color: gray; /* 97: Сив цвят */
            margin-bottom: 3px; /* 98: Мъничко място под часа */
        } /* 99: Край на forecast-time */

        .forecast-temp { /* 100: Правила за температурата в прогнозата */
            font-size: 14px; /* 101: Средни букви - 14 пиксела */
            font-weight: bold; /* 102: Дебели числа */
            color: black; /* 103: Черен цвят */
        } /* 104: Край на forecast-temp */

        /* ═══════ СПЕЦИАЛНИ ПРАВИЛА ЗА ТЕЛЕФОНИ ═══════ */
        @media (max-width: 480px) { /* 105: Ако екранът е малък (телефон)... */
            .forecast-items { /* 106: Променяме мрежата за прогнозата */
                grid-template-columns: repeat(2, 1fr); /* 107: Само 2 колони вместо 4 */
            } /* 108: Край на промените за малки екрани */
        } /* 109: Край на специалните правила */
    </style> <!-- 110: Край на всички стилове -->
</head> <!-- 111: Край на "главата" на документа -->
<body> <!-- 112: Начало на "тялото" - това се вижда на екрана! -->
    <div class="weather-app"> <!-- 113: Главната бяла кутия започва тук -->

        <!-- ═══════ ЧАСТТА ЗА ИЗБОР НА ДЪРЖАВА ═══════ -->
        <div class="country-selector"> <!-- 114: Кутия за избора на държава -->
            <label for="countrySelect" style="display: block; margin-bottom: 10px; font-weight: bold;">Select Country:</label> <!-- 115: Текст "Select Country:" с малко стилове -->
            <select id="countrySelect" class="country-dropdown" onchange="showWeather(this.value)" aria-label="Select country for weather information"> <!-- 116: Падащо меню - при промяна извиква функция showWeather -->
                <option value="bulgaria">Bulgaria</option> <!-- 117: Първи избор - България -->
                <option value="uk">United Kingdom</option> <!-- 118: Втори избор - Великобритания -->
            </select> <!-- 119: Край на падащото меню -->
        </div> <!-- 120: Край на частта за избор -->

        <!-- ═══════ ГЛАВНАТА ИНФОРМАЦИЯ ЗА ВРЕМЕТО ═══════ -->
        <header class="header"> <!-- 121: Горната част с основната информация -->
            <h1 class="location" id="location">Sofia, Bulgaria</h1> <!-- 122: Показва града - започваме със София -->
            <div class="temperature" id="temperature" aria-label="Current temperature">22°</div> <!-- 123: Големите числа за температурата - започваме с 22° -->
            <p class="description" id="description">Sunny</p> <!-- 124: Описание - започваме със "Слънчево" -->
        </header> <!-- 125: Край на горната част -->

        <!-- ═══════ КУТИЙКИТЕ С ДЕТАЙЛИ ═══════ -->
        <div class="details"> <!-- 126: Контейнер за двете кутийки -->
            <div class="detail-box"> <!-- 127: Първа кутийка - за вятъра -->
                <div class="detail-title">Wind Speed</div> <!-- 128: Заглавие "Скорост на вятъра" -->
                <div class="detail-value" id="windSpeed">15 km/h</div> <!-- 129: Показва скоростта - започваме с 15 km/h -->
            </div> <!-- 130: Край на кутийката за вятър -->
            <div class="detail-box"> <!-- 131: Втора кутийка - за влажността -->
                <div class="detail-title">Humidity</div> <!-- 132: Заглавие "Влажност" -->
                <div class="detail-value" id="humidity">5%</div> <!-- 133: Показва влажността - започваме с 5% -->
            </div> <!-- 134: Край на кутийката за влажност -->
        </div> <!-- 135: Край на детайлите -->

        <!-- ═══════ ПРОГНОЗА ЗА ДЕНЯ ═══════ -->
        <div class="forecast"> <!-- 136: Контейнер за прогнозата -->
            <div class="forecast-title">Today's Forecast (Every 3 Hours)</div> <!-- 137: Заглавие "Днешна прогноза (на всеки 3 часа)" -->
            <div class="forecast-items" id="forecastItems"> <!-- 138: Тук JavaScript ще сложи малките кутийки -->
                <!-- 139: Празно място - JavaScript ще го напълни с информация! -->
            </div> <!-- 140: Край на контейнера за кутийките -->
        </div> <!-- 141: Край на прогнозата -->

    </div> <!-- 142: Край на главната бяла кутия -->

    <!-- ═══════ JAVASCRIPT - МОЗЪКЪТ НА ПРИЛОЖЕНИЕТО! ═══════ -->
    <script> <!-- 143: Тук започва JavaScript кодът - той прави страницата "жива"! -->
    
    // 144: Функция showWeather - тя взима информация за времето и я показва
    async function showWeather(country) { // 145: Започваме функцията, която приема държава
        try { // 146: Опитваме се да направим нещо (ако се счупи, ще хванем грешката)
            
            // 147: Питаме сървъра за времето - като звъним на приятел!
            const response = await fetch(`http://localhost:5240/weather/${country}`); // 148: Изпращаме заявка на адрес localhost:5240

            // 149: Проверяваме дали отговорът е добър
            if (!response.ok) { // 150: Ако не е ОК...
                alert("Няма данни за " + country); // 151: Показваме съобщение
                return; // 152: Спираме тук - няма смисъл да продължаваме
            }

            // 153: Превръщаме отговора в JavaScript обект - като отваряме подарък!
            const data = await response.json(); // 154: JSON е формат за данни

            // ═══════ ОБНОВЯВАМЕ ИНФОРМАЦИЯТА НА ЕКРАНА ═══════
            // 155: Намираме елемента за локация и слагаме новия текст
            document.getElementById('location').textContent = data.location || 'Unknown Location'; // 156: Ако няма локация, пишем "Unknown"
            
            // 157: Обновяваме температурата
            document.getElementById('temperature').textContent = data.temperature || 'N/A'; // 158: Ако няма температура, пишем "N/A"
            
            // 159: Обновяваме описанието
            document.getElementById('description').textContent = data.description || 'No description'; // 160: Ако няма описание...
            
            // 161: Обновяваме скоростта на вятъра
            document.getElementById('windSpeed').textContent = data.windSpeed || 'N/A'; // 162: Нова скорост на вятъра
            
            // 163: Обновяваме влажността
            document.getElementById('humidity').textContent = data.humidity || 'N/A'; // 164: Нова влажност

            // ═══════ ПЪЛНИМ ПРОГНОЗАТА ═══════
            // 165: Намираме контейнера за прогнозата
            const forecastContainer = document.getElementById('forecastItems'); // 166: Това е кутията за малките прогнози
            
            // 167: Изтриваме старата прогноза - като изтриваме дъската!
            forecastContainer.innerHTML = ''; // 168: Празен стринг = празна кутия

            // 169: Проверяваме дали има прогноза и дали е списък
            if (data.forecast && Array.isArray(data.forecast)) { // 170: Ако има прогноза...
                
                // 171: За всеки елемент от прогнозата...
                data.forecast.forEach(f => { // 172: forEach минава през всеки елемент
                    
                    // 173: Създаваме нова кутийка
                    const item = document.createElement('div'); // 174: Правим нов div елемент
                    
                    // 175: Даваме му клас forecast-item
                    item.className = 'forecast-item'; // 176: Така получава стиловете
                    
                    // 177: Слагаме HTML съдържание в кутийката
                    item.innerHTML = ` 
                        <div class="forecast-time">${f.time}</div>
                        <div class="forecast-temp">${f.temp}</div>
                    `; // 178: ${} вмъква стойности в текста
                    
                    // 179: Добавяме кутийката към контейнера
                    forecastContainer.appendChild(item); // 180: appendChild = добави дете
                }); // 181: Край на forEach цикъла
            } // 182: Край на проверката за прогноза
            
        } catch (error) { // 183: Ако нещо се счупи, хващаме грешката тук
            console.error('Error fetching weather data:', error); // 184: Записваме грешката в конзолата
            alert('Грешка при зареждане на данните за времето. Моля, опитайте отново.'); // 185: Показваме съобщение на потребителя
        } // 186: Край на try-catch блока
    } // 187: Край на функцията showWeather

    // ═══════ СТАРТИРАМЕ ПРИЛОЖЕНИЕТО ═══════
    // 188: Когато страницата се зареди, показваме времето за България
    showWeather('bulgaria'); // 189: Извикваме функцията с 'bulgaria'

    </script> <!-- 190: Край на JavaScript кода -->

</body> <!-- 191: Край на "тялото" на страницата -->
</html> <!-- 192: Край на целия HTML документ - готово! -->
