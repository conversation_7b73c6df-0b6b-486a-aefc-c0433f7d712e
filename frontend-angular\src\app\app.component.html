<!-- 🏠 Главната кутия на приложението за времето -->
<div class="weather-app">

  <!-- 🔄 Бутон за превключване между режими -->
  <div class="mode-toggle">
    <button
      (click)="toggleSearchMode()"
      class="toggle-button"
      [class.active]="isSearchMode">
      {{ isSearchMode ? '📍 Select from List' : '🔍 Search City' }}
    </button>
  </div>

  <!-- 🌍 Секция за избор на държава (показва се само когато не търсим) -->
  <div class="country-selector" *ngIf="!isSearchMode">
    <label for="countrySelect">Select Country:</label>
    <!-- 🆔 Даваме име на менюто -->
    <select
      id="countrySelect"
      class="country-dropdown"
      [(ngModel)]="selectedCountry"
      (change)="onCountryChange()"
      aria-label="Select country for weather information">
      <!-- 🔄 За всяка държава от списъка... -->
      <option *ngFor="let country of countries" [value]="country.value">
        {{ country.name }}
      </option>
    </select>
    <!-- Край на падащото меню -->
  </div>

  <!-- 🔍 Секция за търсене на град (показва се само в режим на търсене) -->
  <div class="city-search" *ngIf="isSearchMode">
    <label for="cityInput">Search City:</label>
    <div class="search-container">
      <input
        id="cityInput"
        type="text"
        class="city-input"
        [(ngModel)]="searchCity"
        (keypress)="onSearchKeyPress($event)"
        placeholder="Enter city name (e.g., London, Paris, Tokyo)"
        aria-label="Enter city name to search for weather">
      <button
        (click)="searchWeather()"
        class="search-button"
        [disabled]="!searchCity.trim() || isLoading">
        🔍 Search
      </button>
    </div>
  </div>

  <!-- ⏳ Показваме спинер докато зарежда -->
  <div *ngIf="isLoading" class="loading">
    <p>Loading weather data...</p>
  </div>

  <!-- ❌ Показваме грешка ако има проблем -->
  <div *ngIf="hasError && !isLoading" class="error">
    <p>{{ errorMessage || 'Could not load weather data. Please try again.' }}</p>
    <button
      (click)="isSearchMode ? searchWeather() : loadWeather()"
      class="retry-button">
      Retry
    </button>
  </div>

  <!-- 🌤️ Показваме данните за времето -->
  <div *ngIf="weatherData && !isLoading && !hasError">
    
    <!-- 📍 Главна информация -->
    <header class="header">
      <h1 class="location">{{ weatherData.location }}</h1>
      <!-- 🌡️ Кутия за температурата -->
      <div class="temperature" aria-label="Current temperature">
        {{ weatherData.temperature }}
      </div>
      <p class="description">{{ weatherData.description }}</p>
    </header>

    <!-- 📊 Детайли за вятър и влажност -->
    <div class="details">
      <!-- 💨 Кутийка за вятъра -->
      <div class="detail-box">
        <div class="detail-title">Wind Speed</div>
        <div class="detail-value">{{ weatherData.windSpeed }}</div>
      </div>
      
      <!-- 💧 Кутийка за влажността -->
      <div class="detail-box">
        <div class="detail-title">Humidity</div>
        <div class="detail-value">{{ weatherData.humidity }}</div>
      </div>
    </div>

    <!-- 📅 Прогноза за деня -->
    <div class="forecast" *ngIf="weatherData.forecast && weatherData.forecast.length > 0">
      <div class="forecast-title">Today's Forecast (Every 3 Hours)</div>
      <div class="forecast-items">
        <!-- 🔄 За всеки час от прогнозата... -->
        <div class="forecast-item" *ngFor="let item of weatherData.forecast">
          <div class="forecast-time">{{ item.time }}</div>
          <div class="forecast-temp">{{ item.temp }}</div>
        </div>
      </div>
    </div>
    
  </div>
</div>
