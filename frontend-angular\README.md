# 🌦️ Weather Station - Angular Frontend
<!-- Това е README файл - като инструкция за употреба! -->

Това е Angular версията на Weather Station frontend приложението.

## ✨ Характеристики

- 🌍 Избор на държава (България & UK) <!-- Можеш да избираш между две държави -->
- 🌡️ Показване на текущото време (температура, описание, вятър, влажност) <!-- Виждаш всичко важно -->
- 📊 24-часова прогноза (на всеки 3 часа) <!-- Знаеш какво ще бъде времето през деня -->
- 📱 Responsive дизайн <!-- Работи на телефон, таблет и компютър -->
- ⚡ Създадено с Angular 17 (standalone компоненти) <!-- Използва най-новата версия -->
- 🎨 Модерен UI с hover ефекти и преходи <!-- Красиво и интерактивно -->

## 📋 Изисквания

- Node.js (v18 или по-нова версия) <!-- Трябва ти Node.js инсталиран -->
- npm (v9 или по-нова версия) <!-- Идва с Node.js -->
- Backend API на `http://localhost:5240` <!-- Сървърът трябва да работи -->

## 🚀 Инсталация

1. Отиди в frontend-angular папката:
   ```bash
   cd frontend-angular  # 📁 Влизаме в папката
   ```

2. Инсталирай зависимостите:
   ```bash
   npm install  # 📦 Сваля всички необходими библиотеки
   ```

## ▶️ Стартиране на приложението

1. Първо стартирай backend API:
   ```bash
   cd ../backend/WeatherApi  # 📁 Отиваме в backend папката
   dotnet run  # 🚀 Стартираме .NET сървъра
   ```

2. Стартирай Angular development сървъра:
   ```bash
   npm start  # 🌟 Стартира приложението
   ```

3. Отвори браузъра на `http://localhost:3000` <!-- 🌐 Тук ще видиш приложението -->

## 📁 Структура на проекта

```
frontend-angular/
├── src/                              # 📂 Изходен код
│   ├── app/                          # 🏠 Главната папка на приложението
│   │   ├── models/                   # 📋 TypeScript интерфейси (формички за данни)
│   │   │   └── weather.model.ts      # 🌦️ Модели за времето
│   │   ├── services/                 # 🔧 Сервиси
│   │   │   └── weather.service.ts    # 📞 Сервис за HTTP заявки
│   │   ├── app.component.ts          # 🧠 Главна логика на компонента
│   │   ├── app.component.html        # 🖼️ HTML шаблон
│   │   └── app.component.css         # 🎨 Стилове на компонента
│   ├── index.html                    # 📄 Главен HTML файл
│   ├── main.ts                       # 🚀 Стартова точка на приложението
│   └── styles.css                    # 🌍 Глобални стилове
├── angular.json                      # ⚙️ Angular конфигурация
├── tsconfig.json                     # 📘 TypeScript конфигурация
└── package.json                      # 📦 Зависимости и скриптове
```

## 🛠️ Използвани технологии

- Angular 17 (със standalone компоненти) <!-- 🅰️ Най-новата версия -->
- TypeScript <!-- 📘 По-безопасен JavaScript -->
- RxJS за реактивно програмиране <!-- 🎭 За асинхронни операции -->
- Angular HttpClient за API заявки <!-- 📞 За комуникация със сървъра -->
- CSS Grid за responsive layout <!-- 🎲 За подредба на елементите -->

## 🔄 Разлики от Vanilla JS версията

1. **Type Safety**: Пълна TypeScript поддръжка с интерфейси <!-- 🛡️ По-малко грешки -->
2. **Компонентна архитектура**: Организиран код <!-- 📦 По-лесен за поддръжка -->
3. **Реактивно програмиране**: Използване на RxJS Observables <!-- 🌊 По-добра работа с данни -->
4. **Two-way Data Binding**: Опростена работа с форми <!-- 🔄 Автоматични обновявания -->
5. **Подобрена обработка на грешки**: По-добра обратна връзка <!-- ❌ По-ясни съобщения -->
6. **Loading състояния**: Визуална обратна връзка по време на зареждане <!-- ⏳ Виждаш кога зарежда -->
7. **Hover ефекти**: Добавени интерактивни елементи <!-- 🖱️ По-приятно за използване -->

## 🏗️ Компилиране за продукция

```bash
npm run build  # 🏭 Прави оптимизирана версия
```

Готовите файлове ще бъдат в папка `dist/`. <!-- 📦 Готови за качване на сървър -->

<!-- 🎉 Готово! Вече знаеш всичко за Angular Weather Station! -->
