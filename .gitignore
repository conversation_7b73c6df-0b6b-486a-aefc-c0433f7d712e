# Dependencies
node_modules/
**/node_modules/

# Angular build outputs
dist/
**/dist/
.angular/
**/.angular/

# IDE - Visual Studio Code
.vscode/
*.code-workspace

# IDE - Visual Studio
.vs/
**/.vs/
*.suo
*.user
*.userosscache
*.sln.docstates

# .NET build outputs
bin/
obj/
*.dll
*.exe
*.pdb

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS files
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
*.swp
*~

# Test coverage
coverage/
.nyc_output/

# Environment files (keep .env.example)
.env
.env.local
.env.production

# Package lock files (optional - some prefer to commit these)
# Uncomment if you don't want to track lock files
# package-lock.json
# yarn.lock

# Build artifacts
*.zip
*.tar.gz

# Debug files
npm-debug.log*
debug.log

# Local batch files for testing
test-*.bat
temp-*.js
