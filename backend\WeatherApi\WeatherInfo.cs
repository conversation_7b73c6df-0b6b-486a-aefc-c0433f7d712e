// 🌦️ Това е файлът с класовете за времето - като формички за данни!
using System.Collections.Generic; // 📦 Включваме System.Collections.Generic, за да можем да използваме List<T> (списъци)

/* 🌡️ ---------- Клас за единичен елемент от прогнозата ---------- */
public class ForecastItem        // 📊 Дефинираме клас ForecastItem – представлява един часови прозорец
{                                // 🏗️ от прогнозата (примерно 03:00 с 12 °С)
    public string? Time { get; set; } // ⏰ Свойство Time – часово време като текст, напр. "03:00"
                                      // ❓ Знакът ? означава, че може да бъде null (празно)
                                      // 📝 get - можем да четем стойността
                                      // ✏️ set - можем да променяме стойността
    
    public string? Temp { get; set; } // 🌡️ Свойство Temp – температура като текст, напр. "12°"
                                      // ❓ Отново може да бъде null
                                      // 📊 Използваме string вместо число за простота
} // 🏁 Край на класа ForecastItem

/* 🌤️ ---------- Главният клас, който ще връщаме от API-то ---------- */
public class WeatherInfo         // 🏠 Дефинираме клас WeatherInfo – съдържа цялата информация за дадена държава / град
{                               // 📦 Това е като голяма кутия с всички данни за времето
    public string? Location { get; set; }    // 📍 Име на мястото, напр. "Sofia, Bulgaria"
                                            // 🗺️ Показва се най-горе в приложението
    
    public string? Temperature { get; set; } // 🌡️ Текуща температура, напр. "22°"
                                            // 🔢 Големите сини числа в приложението
    
    public string? Description { get; set; } // ☀️ Кратко описание, напр. "Sunny"
                                            // 📝 Показва се под температурата
    
    public string? WindSpeed { get; set; }   // 💨 Скорост на вятъра, напр. "15 km/h"
                                            // 🌬️ Показва се в лявата кутийка
    
    public string? Humidity { get; set; }    // 💧 Влажност, напр. "5%"
                                            // 💦 Показва се в дясната кутийка

    // 📊 Прогнозата – списък от ForecastItem обекти
    public List<ForecastItem> Forecast { get; set; } = new(); // 📋 Списък с прогнози за различни часове
                                                              // 🆕 new() създава празен списък
                                                              // 📦 Ще се напълни с 8 часа (на всеки 3 часа)
} // 🏁 Край на класа WeatherInfo

/* 
📚 Обобщение:
- ForecastItem = един час от прогнозата (малка кутийка)
- WeatherInfo = всички данни за времето (голямата картина)
- Използваме string? за всички стойности (лесно за показване)
- List<ForecastItem> държи всички часове от прогнозата
*/
