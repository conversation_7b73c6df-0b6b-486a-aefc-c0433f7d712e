// 🌍 Environment configuration for development
// Тук съхраняваме настройки, които се различават между development и production

export const environment = {
  production: false, // 🚧 Това е development режим
  
  // 🌦️ OpenWeatherMap API настройки
  openWeatherMap: {
    // 🔑 API ключ за OpenWeatherMap (безплатен план - 1000 заявки на ден)
    // 📝 За да получите свой ключ: https://openweathermap.org/api
    // ⚠️ ВАЖНО: В реален проект този ключ трябва да се скрие!
    // 🔄 ЗАМЕНЕТЕ С ВАШИЯ API КЛЮЧ ПРЕДИ ТЕСТВАНЕ!
    apiKey: 'YOUR_API_KEY_HERE', // 🔄 Заменете с вашия API ключ
    
    // 🌐 Base URL за OpenWeatherMap API
    baseUrl: 'https://api.openweathermap.org/data/2.5',
    
    // 🌡️ Единици за измерване (metric = Celsius, imperial = Fahrenheit, standard = Kelvin)
    units: 'metric',
    
    // 🗣️ Език за описанията (bg = български, en = английски)
    language: 'en'
  }
};

// 📝 Инструкции за настройка:
// 1. Отидете на https://openweathermap.org/api
// 2. Създайте безплатен акаунт
// 3. Получете вашия API ключ от Dashboard -> API keys
// 4. Заменете 'YOUR_API_KEY_HERE' с вашия ключ
// 5. Запазете файла и рестартирайте приложението
