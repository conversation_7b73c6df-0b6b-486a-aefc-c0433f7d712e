// 🔧 Това е сервисът - като помощник, който носи данни за времето
import { Injectable } from '@angular/core'; // 💉 Взимаме "инжекцията" - специална Angular магия
import { HttpClient, HttpParams } from '@angular/common/http'; // 📞 Взимаме "телефона" за говорене със сървъра
import { Observable, catchError, of, map } from 'rxjs'; // 🎭 Взимаме специални инструменти за асинхронни операции
import { WeatherInfo, OpenWeatherResponse } from '../models/weather.model'; // 📋 Взимаме нашите "формички" за времето
import { environment } from '../../environments/environment'; // 🌍 Взимаме настройките

// 🏷️ Декоратор - казва на Angular, че това е сервис
@Injectable({ // Слагаме етикет "Аз съм сервис!"
  providedIn: 'root' // 🌳 Казваме: "Този сервис е достъпен навсякъде в приложението"
}) // Край на етикета

// 🌦️ Класът WeatherService - нашият помощник за времето
export class WeatherService { // Започваме да правим помощника

  // 🗺️ Речник с градове за различните "държави" (за съвместимост с текущия UI)
  private readonly cityMapping: { [key: string]: string } = {
    'bulgaria': 'Sofia,BG',      // 🇧🇬 София, България
    'uk': 'London,GB',           // 🇬🇧 Лондон, Великобритания
    'usa': 'New York,US',        // 🇺🇸 Ню Йорк, САЩ
    'germany': 'Berlin,DE',      // 🇩🇪 Берлин, Германия
    'france': 'Paris,FR',        // 🇫🇷 Париж, Франция
    'italy': 'Rome,IT',          // 🇮🇹 Рим, Италия
    'spain': 'Madrid,ES',        // 🇪🇸 Мадрид, Испания
    'greece': 'Athens,GR'        // 🇬🇷 Атина, Гърция
  };

  // 🔨 Конструктор - специална функция, която се изпълнява при създаване на сервиса
  constructor(private http: HttpClient) { } // Получаваме HttpClient като подарък от Angular

  /**
   * 🌍 Взима информация за времето за дадена държава от OpenWeatherMap API
   * @param country - кодът на държавата (напр. 'bulgaria', 'uk')
   * @returns Observable с информацията за времето (като обещание за данни)
   */
  getWeather(country: string): Observable<WeatherInfo | null> { // Функция, която взима времето
    // 🗺️ Намираме града за тази държава
    const cityQuery = this.cityMapping[country.toLowerCase()];

    if (!cityQuery) { // ❓ Ако няма такава държава в нашия речник...
      console.error(`Неизвестна държава: ${country}`); // 📢 Викаме в конзолата
      return of(null); // Връщаме празни ръце
    }

    // 🔑 Проверяваме дали имаме API ключ
    if (!environment.openWeatherMap.apiKey || environment.openWeatherMap.apiKey === 'YOUR_API_KEY_HERE') {
      console.error('❌ Няма настроен API ключ за OpenWeatherMap! Моля, настройте го в environment.ts');
      return of(null);
    }

    // 📋 Подготвяме параметрите за API заявката
    const params = new HttpParams()
      .set('q', cityQuery) // 🏙️ Града и държавата
      .set('appid', environment.openWeatherMap.apiKey) // 🔑 API ключа
      .set('units', environment.openWeatherMap.units) // 🌡️ Единиците (metric = Celsius)
      .set('lang', environment.openWeatherMap.language); // 🗣️ Езика

    // 🌐 URL за OpenWeatherMap API
    const apiUrl = `${environment.openWeatherMap.baseUrl}/weather`;

    // 📤 Правим заявка към OpenWeatherMap API
    return this.http.get<OpenWeatherResponse>(apiUrl, { params }) // Звъним на OpenWeatherMap
      .pipe( // 🚰 Използваме "тръба" за обработка на отговора
        map(response => this.transformToWeatherInfo(response)), // 🔄 Превръщаме отговора в наш формат
        catchError(error => { // 🪤 Ако хванем грешка...
          console.error('Грешка при зареждане на данните за времето от OpenWeatherMap:', error); // 📢 Викаме в конзолата

          // 🔍 Показваме по-подробна информация за грешката
          if (error.status === 401) {
            console.error('❌ Невалиден API ключ! Моля, проверете вашия OpenWeatherMap API ключ.');
          } else if (error.status === 404) {
            console.error('❌ Градът не е намерен! Проверете името на града.');
          } else if (error.status === 429) {
            console.error('❌ Превишен лимит на заявки! Опитайте отново по-късно.');
          }

          // 🤷 Връщаме null при грешка (празни ръце)
          return of(null); // Казваме "съжалявам, нямам данни"
        }) // Край на хващането на грешки
      ); // Край на тръбата
  } // Край на функцията getWeather

  /**
   * 🔄 Превръща OpenWeatherMap отговор в наш WeatherInfo формат
   * @param response - отговорът от OpenWeatherMap API
   * @returns WeatherInfo обект в нашия формат
   */
  private transformToWeatherInfo(response: OpenWeatherResponse): WeatherInfo {
    // 🌡️ Форматираме температурата
    const temperature = `${Math.round(response.main.temp)}°`;

    // 💨 Форматираме скоростта на вятъра (от m/s в km/h)
    const windSpeedKmh = Math.round(response.wind.speed * 3.6);
    const windSpeed = `${windSpeedKmh} km/h`;

    // 💧 Форматираме влажността
    const humidity = `${response.main.humidity}%`;

    // 📍 Форматираме локацията
    const location = `${response.name}, ${response.sys.country}`;

    // 📝 Взимаме описанието (първото условие за времето)
    const description = response.weather[0]?.description || 'Unknown';

    // 📊 Създаваме фалшива прогноза (OpenWeatherMap Current API не дава прогноза)
    // В реален проект бихме използвали 5-day forecast API
    const forecast = this.generateMockForecast(response.main.temp);

    return {
      location,
      temperature,
      description,
      windSpeed,
      humidity,
      forecast
    };
  }

  /**
   * 📊 Генерира фалшива прогноза за демонстрация
   * (В реален проект бихме използвали OpenWeatherMap 5-day forecast API)
   * @param baseTemp - базовата температура
   * @returns масив с прогнозни елементи
   */
  private generateMockForecast(baseTemp: number) {
    const hours = ['12:00', '15:00', '18:00', '21:00', '00:00', '03:00', '06:00', '09:00'];
    const tempVariations = [2, 4, 1, -2, -4, -5, -3, 0]; // Вариации в температурата

    return hours.map((time, index) => ({
      time,
      temp: `${Math.round(baseTemp + tempVariations[index])}°`
    }));
  }

  /**
   * 🔍 Търси времето по име на град (нова функционалност)
   * @param cityName - името на града
   * @returns Observable с информацията за времето
   */
  getWeatherByCity(cityName: string): Observable<WeatherInfo | null> {
    // 🔑 Проверяваме дали имаме API ключ
    if (!environment.openWeatherMap.apiKey || environment.openWeatherMap.apiKey === 'YOUR_API_KEY_HERE') {
      console.error('❌ Няма настроен API ключ за OpenWeatherMap!');
      return of(null);
    }

    // 📋 Подготвяме параметрите за API заявката
    const params = new HttpParams()
      .set('q', cityName) // 🏙️ Името на града
      .set('appid', environment.openWeatherMap.apiKey) // 🔑 API ключа
      .set('units', environment.openWeatherMap.units) // 🌡️ Единиците
      .set('lang', environment.openWeatherMap.language); // 🗣️ Езика

    // 🌐 URL за OpenWeatherMap API
    const apiUrl = `${environment.openWeatherMap.baseUrl}/weather`;

    // 📤 Правим заявка към OpenWeatherMap API
    return this.http.get<OpenWeatherResponse>(apiUrl, { params })
      .pipe(
        map(response => this.transformToWeatherInfo(response)),
        catchError(error => {
          console.error('Грешка при търсене на град:', error);
          return of(null);
        })
      );
  }
} // Край на класа WeatherService
