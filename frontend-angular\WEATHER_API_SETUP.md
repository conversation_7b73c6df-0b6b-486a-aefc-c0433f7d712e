# 🌦️ Weather API Setup Instructions

## 📋 Overview
This Angular application now fetches real weather data from OpenWeatherMap API instead of using mock data from the backend.

## 🔑 Getting Your Free API Key

1. **Visit OpenWeatherMap**: Go to [https://openweathermap.org/api](https://openweathermap.org/api)

2. **Sign Up**: Create a free account if you don't have one

3. **Get API Key**: 
   - Go to your account dashboard
   - Click on "API keys" tab
   - Copy your default API key (or create a new one)

4. **Free Tier Limits**:
   - ✅ 1,000 API calls per day
   - ✅ Current weather data
   - ✅ 5-day forecast (not implemented yet)
   - ✅ No credit card required

## ⚙️ Configuration

1. **Open Environment File**: 
   ```
   frontend-angular/src/environments/environment.ts
   ```

2. **Replace API Key**:
   ```typescript
   export const environment = {
     production: false,
     openWeatherMap: {
       apiKey: 'YOUR_ACTUAL_API_KEY_HERE', // 👈 Replace this!
       baseUrl: 'https://api.openweathermap.org/data/2.5',
       units: 'metric',
       language: 'en'
     }
   };
   ```

3. **Save and Restart**: Save the file and restart the Angular development server

## 🚀 Running the Application

1. **Start Angular Dev Server**:
   ```bash
   cd frontend-angular
   npm start
   ```

2. **Open Browser**: Navigate to `http://localhost:4200`

## ✨ New Features

### 🌍 Country Selection
- Choose from 8 predefined countries
- Each country maps to a major city:
  - Bulgaria 🇧🇬 → Sofia
  - United Kingdom 🇬🇧 → London
  - United States 🇺🇸 → New York
  - Germany 🇩🇪 → Berlin
  - France 🇫🇷 → Paris
  - Italy 🇮🇹 → Rome
  - Spain 🇪🇸 → Madrid
  - Greece 🇬🇷 → Athens

### 🔍 City Search
- Click "🔍 Search City" to switch to search mode
- Enter any city name (e.g., "Tokyo", "Sydney", "Cairo")
- Press Enter or click Search button
- Switch back to country list with "📍 Select from List"

### 📊 Real Weather Data
- **Current Temperature**: Live data in Celsius
- **Weather Description**: Current conditions (sunny, cloudy, rainy, etc.)
- **Wind Speed**: Converted from m/s to km/h
- **Humidity**: Percentage
- **Location**: City and country name
- **Mock Forecast**: 8-hour forecast (simulated for demo)

## 🛠️ Technical Details

### API Endpoints Used
- **Current Weather**: `https://api.openweathermap.org/data/2.5/weather`
- **Parameters**: 
  - `q`: City name and country code
  - `appid`: Your API key
  - `units`: metric (Celsius)
  - `lang`: en (English)

### Error Handling
- ❌ Invalid API key detection
- ❌ City not found handling
- ❌ Rate limit exceeded warnings
- ❌ Network error recovery

### Mobile Responsive
- 📱 Optimized for mobile devices
- 📱 Touch-friendly buttons
- 📱 Responsive search interface

## 🔧 Troubleshooting

### "API Key Error"
- Check that you've replaced `YOUR_API_KEY_HERE` with your actual key
- Verify the API key is active in your OpenWeatherMap dashboard
- Wait a few minutes after creating a new key (activation delay)

### "City Not Found"
- Try different spelling or language
- Use format: "City, Country" (e.g., "Paris, FR")
- Check if the city exists in OpenWeatherMap database

### "Rate Limit Exceeded"
- You've made more than 1,000 calls today
- Wait until tomorrow or upgrade your plan
- Consider caching responses in a real application

## 🎯 Next Steps

To enhance this application further, you could:

1. **Add 5-Day Forecast**: Use OpenWeatherMap's forecast API
2. **Add Weather Maps**: Integrate weather map layers
3. **Add Geolocation**: Auto-detect user's location
4. **Add Favorites**: Save favorite cities
5. **Add Weather Alerts**: Show severe weather warnings
6. **Add Charts**: Display temperature trends
7. **Add Caching**: Store recent searches locally

## 📚 Resources

- [OpenWeatherMap API Documentation](https://openweathermap.org/api)
- [Angular HttpClient Guide](https://angular.io/guide/http)
- [Angular Environment Configuration](https://angular.io/guide/build#configuring-application-environments)
