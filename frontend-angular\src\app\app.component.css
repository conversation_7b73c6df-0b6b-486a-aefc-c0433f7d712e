/* 🎨 Стилове за weather app компонента - тук правим приложението красиво! */

/* 🏠 Главната бяла кутия на приложението */
.weather-app { /* Започваме да описваме главната кутия */
  background-color: white; /* 🎨 Боядисваме я в бяло - като облаче! */
  max-width: 500px; /* 📏 Не може да стане по-широка от 500 пиксела */
  margin: 0 auto; /* 🎯 Магия! Това я слага точно в средата */
  padding: 20px; /* 🛏️ Слагаме "възглавничка" от 20 пиксела отвътре */
  border-radius: 10px; /* 🍭 Правим ъглите заоблени - като бонбон! */
  box-shadow: 0 4px 8px gray; /* 🌫️ Добавяме сянка - сякаш кутията лети! */
} /* Край на стиловете за главната кутия */

/* 🌍 Стилове за избор на държава */
.country-selector { /* Кутията с падащото меню */
  text-align: center; /* 🎯 Слагаме текста в центъра */
  margin-bottom: 20px; /* ⬇️ Оставяме 20 пиксела място отдолу */
} /* Край на country-selector */

/* 🏷️ Стилове за етикета "Select Country" */
.country-selector label { /* Текстът над менюто */
  display: block; /* 📦 Заема цял ред */
  margin-bottom: 10px; /* ⬇️ 10 пиксела място отдолу */
  font-weight: bold; /* 💪 Правим буквите дебели */
} /* Край на етикета */

/* 📋 Стилове за падащото меню */
.country-dropdown { /* Самото меню за избор */
  background-color: lightgray; /* 🌫️ Светлосив фон - като мъгла */
  border: 2px solid gray; /* 🖼️ Рамка от 2 пиксела в сиво */
  padding: 10px 20px; /* 🛏️ "Възглавничка" - 10 горе/долу, 20 ляво/дясно */
  border-radius: 5px; /* 🍬 Леко заоблени ъгли */
  font-size: 16px; /* 📝 Правим буквите с размер 16 */
  cursor: pointer; /* 👆 Когато минем с мишката - става ръчичка! */
  transition: background-color 0.3s ease; /* 🎬 Плавна промяна на цвета за 0.3 секунди */
} /* Край на dropdown стиловете */

/* 🖱️ Какво се случва когато минем с мишката върху менюто */
.country-dropdown:hover { /* При посочване с мишката */
  background-color: #e0e0e0; /* 🎨 Става малко по-тъмно сиво */
} /* Край на hover ефекта */

/* ⏳ Стилове за зареждане и грешки */
.loading, .error { /* И двете имат еднакви стилове */
  text-align: center; /* 🎯 Текстът в средата */
  padding: 20px; /* 🛏️ 20 пиксела "възглавничка" */
} /* Край на loading и error */

/* ❌ Специални стилове само за грешката */
.error { /* Когато има грешка */
  color: #d32f2f; /* 🔴 Червен цвят за текста */
} /* Край на error */

/* 🔄 Стилове за бутона "Retry" */
.retry-button { /* Бутонът за опитай пак */
  margin-top: 10px; /* ⬆️ 10 пиксела отгоре */
  padding: 8px 16px; /* 🛏️ Възглавничка 8 горе/долу, 16 ляво/дясно */
  background-color: #1976d2; /* 🔵 Син фон */
  color: white; /* ⚪ Бял текст */
  border: none; /* ❌ Без рамка */
  border-radius: 4px; /* 🍬 Малко заоблени ъгли */
  cursor: pointer; /* 👆 Ръчичка при посочване */
  font-size: 14px; /* 📝 Размер на буквите 14 */
  transition: background-color 0.3s ease; /* 🎬 Плавна промяна на цвета */
} /* Край на retry-button */

/* 🖱️ Retry бутон при посочване */
.retry-button:hover { /* Когато мишката е върху бутона */
  background-color: #1565c0; /* 🔷 По-тъмно синьо */
} /* Край на hover за бутона */

/* 📍 Стилове за header секцията (горната част) */
.header { /* Контейнер за град, температура и описание */
  text-align: center; /* 🎯 Всичко в центъра */
  margin-bottom: 20px; /* ⬇️ 20 пиксела място отдолу */
} /* Край на header */

/* 🏙️ Стилове за името на града */
.location { /* Текстът със София/Лондон */
  font-size: 18px; /* 📏 Буквите са 18 пиксела */
  margin: 0 0 10px 0; /* 📐 0 горе, 0 дясно, 10 долу, 0 ляво */
} /* Край на location */

/* 🌡️ Стилове за температурата */
.temperature { /* Големите числа с градусите */
  font-size: 48px; /* 🔢 МНОГО големи числа - 48 пиксела! */
  font-weight: bold; /* 💪 Дебели числа - като с маркер! */
  color: blue; /* 🔵 Сини числа */
  margin-bottom: 10px; /* ⬇️ 10 пиксела място отдолу */
} /* Край на temperature */

/* ☀️ Стилове за описанието */
.description { /* Текстът "Sunny"/"Rainy" */
  font-size: 16px; /* 📏 Нормален размер - 16 пиксела */
  color: gray; /* 🌫️ Сив цвят - не много тъмен */
  margin: 0; /* ⭕ Без допълнително място */
} /* Край на description */

/* 📊 Стилове за детайлите (вятър и влажност) */
.details { /* Контейнер за двете кутийки */
  display: flex; /* 🎯 Магия! Подрежда кутийките една до друга */
  justify-content: space-around; /* 📏 Разпределя ги равномерно */
  margin: 20px 0; /* ↕️ 20 пиксела място горе и долу */
} /* Край на details */

/* 📦 Стилове за всяка малка кутийка */
.detail-box { /* Кутийка за вятър или влажност */
  text-align: center; /* 🎯 Текстът в средата */
  background-color: lightgray; /* 🌫️ Светлосив фон */
  padding: 15px; /* 🛏️ 15 пиксела "възглавничка" */
  border-radius: 5px; /* 🍬 Леко заоблени ъгли */
  width: 45%; /* 📏 Всяка кутийка заема 45% от мястото */
  transition: transform 0.2s ease; /* 🎬 Плавно движение за 0.2 секунди */
} /* Край на detail-box */

/* 🖱️ Ефект при посочване на кутийка */
.detail-box:hover { /* Когато мишката е върху кутийката */
  transform: translateY(-2px); /* ⬆️ Вдига се с 2 пиксела нагоре */
  box-shadow: 0 4px 8px rgba(0,0,0,0.1); /* 🌫️ Появява се лека сянка */
} /* Край на hover за кутийка */

/* 🏷️ Стилове за заглавията в кутийките */
.detail-title { /* "Wind Speed" или "Humidity" */
  font-size: 14px; /* 📏 По-малки букви - 14 пиксела */
  color: gray; /* 🌫️ Сив цвят */
  margin-bottom: 5px; /* ⬇️ 5 пиксела място отдолу */
} /* Край на detail-title */

/* 📊 Стилове за стойностите в кутийките */
.detail-value { /* "15 km/h" или "5%" */
  font-size: 20px; /* 📏 По-големи числа - 20 пиксела */
  font-weight: bold; /* 💪 Дебели числа */
  color: black; /* ⚫ Черен цвят - да се виждат ясно */
} /* Край на detail-value */

/* 📅 Стилове за прогнозата */
.forecast { /* Контейнер за цялата прогноза */
  margin-top: 20px; /* ⬆️ 20 пиксела място отгоре */
} /* Край на forecast */

/* 📋 Стилове за заглавието на прогнозата */
.forecast-title { /* "Today's Forecast..." */
  font-size: 18px; /* 📏 Размер 18 пиксела */
  font-weight: bold; /* 💪 Дебели букви */
  margin-bottom: 15px; /* ⬇️ 15 пиксела място отдолу */
  text-align: center; /* 🎯 В средата */
} /* Край на forecast-title */

/* 🎲 Стилове за мрежата с часовете */
.forecast-items { /* Контейнер за малките кутийки */
  display: grid; /* 📋 Използваме мрежа - като шахматна дъска! */
  grid-template-columns: repeat(4, 1fr); /* 4️⃣ Правим 4 еднакви колони */
  gap: 10px; /* 📏 10 пиксела между кутийките */
} /* Край на forecast-items */

/* ⏰ Стилове за една кутийка с час */
.forecast-item { /* Една малка кутийка с час и температура */
  text-align: center; /* 🎯 Текстът в средата */
  background-color: lightgray; /* 🌫️ Светлосив фон */
  padding: 8px; /* 🛏️ Малка "възглавничка" от 8 пиксела */
  border-radius: 5px; /* 🍬 Заоблени ъгли */
  transition: all 0.2s ease; /* 🎬 Всички промени стават плавно */
} /* Край на forecast-item */

/* 🖱️ Ефект при посочване на час */
.forecast-item:hover { /* Когато мишката е върху часа */
  background-color: #e0e0e0; /* 🌫️ По-тъмно сиво */
  transform: scale(1.05); /* 🔍 Уголемява се с 5% */
} /* Край на hover за час */

/* ⏰ Стилове за часа в прогнозата */
.forecast-time { /* "12:00", "15:00"... */
  font-size: 11px; /* 📏 Много малки букви - 11 пиксела */
  color: gray; /* 🌫️ Сив цвят */
  margin-bottom: 3px; /* ⬇️ Мъничко място отдолу - 3 пиксела */
} /* Край на forecast-time */

/* 🌡️ Стилове за температурата в прогнозата */
.forecast-temp { /* "26°", "27°"... */
  font-size: 14px; /* 📏 Средни букви - 14 пиксела */
  font-weight: bold; /* 💪 Дебели числа */
  color: black; /* ⚫ Черен цвят */
} /* Край на forecast-temp */

/* 📱 Специални стилове за мобилни устройства */
@media (max-width: 480px) { /* Ако екранът е по-малък от 480 пиксела (телефон) */
  .weather-app { /* Главната кутия на телефон */
    padding: 15px; /* 🛏️ По-малка възглавничка - 15 пиксела */
  } /* Край на промените за главната кутия */

  .temperature { /* Температурата на телефон */
    font-size: 36px; /* 📏 По-малки числа - 36 вместо 48 */
  } /* Край на промените за температурата */

  .details { /* Детайлите на телефон */
    flex-direction: column; /* ↕️ Кутийките една под друга, не една до друга */
    gap: 10px; /* 📏 10 пиксела между тях */
  } /* Край на промените за детайли */

  .detail-box { /* Кутийките на телефон */
    width: 100%; /* 📏 Заемат цялата ширина */
  } /* Край на промените за кутийки */

  .forecast-items { /* Прогнозата на телефон */
    grid-template-columns: repeat(2, 1fr); /* 2️⃣ Само 2 колони вместо 4 */
  } /* Край на промените за прогноза */
} /* Край на специалните правила за телефони */
